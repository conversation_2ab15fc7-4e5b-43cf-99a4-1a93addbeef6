# Changelog

**Tipos de cambios**
- <PERSON><PERSON><PERSON><PERSON> `Added` para funcionalidades nuevas.
- Modi<PERSON>do `Changed` para los cambios en funcionalidades existentes.
- Obsoleto `Deprecated` para indicar que una funcionalidad está obsoleta, se queda sin soporte y se eliminará en próximas versiones.
- Eliminado `Removed` para las funcionalidades en desuso que se eliminaron en esta versión.
- Corregido `Fixed` para corrección de errores.
- Seguridad `Security` en caso de vulnerabilidades.
<br>

---

## Comadreja [**1.3.0**] <sub>2023-05-17</sub>

### Added
    Lógica de consumo de sesión en SSO (Authentication) "/me" del lado del cliente
    Nuevo script en package.json "test:prod" para simular environments de producción
    Anexo a .gitignore archivos autogenerados de .vscode
    Anexo a angular.json especificación en cli '"packageManager":"npm"'
### Changed
    Actualización de core de Angular 14 -> 15
    Actualización de ECMAScript base ES2020 -> ES2022
    Cambio de base href en index.html "./" -> "/" para evitar missmatch en routing
    Reorganización en componentes de módulo hacia folder "components" en apego al estándar de Scaffolding (https://sites.google.com/coppel.com/developers/frameworks/webclient/scaffolding)
    Renombrado de script en package.json "start-shared" -> "start:shared"
    Renombrado de script en package.json "json-start" -> "fake-api-start"
    Patch de compatibilidad de configuración en arquitectura
    Actualización de dependencias con compatibilidad para Angular 15
        "dependencies": {
            "ngx-bootstrap": "^10.0.0",
            "rxjs": "~7.8.0",
            "tslib": "^2.3.0",
            "zone.js": "~0.12.0"
        },
        "devDependencies": {
            "concurrently": "^8.0.0", https://github.com/open-cli-tools/concurrently/compare/v7.6.0...v8.0.0
            "typescript": "~4.9.4" https://devblogs.microsoft.com/typescript/announcing-typescript-4-9/
        },
### Fixed
    Corrección en "full-layout.component.ts" para llamada a notificaciones
    Corrección en error "unmatched pseudo-class :lang" Angular 12 + Bootstrap 4
    Corrección en error de captura de nonce en secure-storage.service.ts
### Removed
    Eliminación de integración con jest corrupta e incompatible con Angular 15
    Eliminación de dependencia "path" sin utilidad en la plantilla 
    Eliminación de polyfills.ts
    Eliminación de filter pipes en base al estándar (https://angular.io/guide/styleguide#do-not-add-filtering-and-sorting-logic-to-pipes)
    Eliminación de funcionalidades search de layout poco usuales
    Eliminación de script package.json "dns" redundante a "start:shared"
    Eliminación de script package.json "dns-json" redundante a "dev:shared"
    Eliminación de script package.json "json-start-shared" debido a que no se requiere para productivo

## Comadreja [**1.2.0**] <sub>2023-03-23</sub>

### Added
    Se agrega función síncrona getCurrentUser en session service
### Changed
    Refactorización de Secure Storage Service
    Se oculta el badge de recaptcha al salir de la pantalla de login para evitar que sea invasivo
    Se espera respuesta de recaptcha sólamente si el método seleccionado es colaboradorDigital
    Se muestra mensaje de fallo detallado tras cambio de respuesta en servicio Authentication
    Cambia nombrado de archivo 'Auth.guard' -> 'auth.guard' en apego al estándar
### Fixed
    Corrección de overload de recaptcha script
    Corrección en environments de comentarios para channel de colaboradorDigital
    Corrección en título en example toaster 'Secure LocalStorage' -> 'Simple Toaster'
### Removed
    Eliminación de entry points index.ts para mejora en el performance https://sandroroth.com/blog/angular-library
    Eliminación de archivo no utilizado 'form-validation-responses.ts'
    Eliminación de Boostrap como recomendación de resources puesto que se planea independizar

## Comadreja [**1.1.4**] <sub>2023-03-02</sub>

### Security
    Versiones de dependencias actualizadas en escaneo checkmarx SCA
        "dependencies": {
            "@azure/msal-angular": "^2.5.3",
            "@azure/msal-browser": "^2.33.0",
            "libsodium-wrappers": "^0.7.11",
            "luxon": "^3.2.1",
            "rxjs": "^7.8.0",
            "tslib": "^2.5.0",
        },
        "devDependencies": {
            "@types/jest": "^29.4.0",
            "jest": "^29.4.0",
            "jest-preset-angular": "^13.0.0",
            "typescript": "~4.8.0"
        }
### Removed
    Eliminación de dependencia insegura concat-typed-array identificada por checkmarx SCA, sustitución de implementación por código nativo
    Eliminación de dependencia de desarollo para el tipado del servicio securestorage @types/libsodium-wrappers

## Comadreja [**1.1.3**] <sub>2022-02-01</sub>

### Added
    Funcionalidades para login custom (temporalmente, en maduración AAD para gestión de usuarios externos)
### Fixed
    Mensaje de error en la platilla cuando no se logra conexión con Authentication (Nuevo SSO)

## Comadreja [**1.1.2**] <sub>2022-12-19</sub>

### Fixed
    SecureStorageService sitio no carga cuando se tienen datos almacenados previamente en storage

## Comadreja [**1.1.1**] <sub>2022-12-14</sub>

### Added
    Nuevo FilterByPropModule 'shared-modules/pipes/filter-by-prop/*'
    Nuevo ExampleFilter 'shared-modules/pipes/filter-by-prop/*'

### Fixed
    Cambio de ubicación SafeResourcePipe a shared-modules

## Comadreja [**1.1.0**] <sub>2022-12-14</sub>

### Added
    Environment templates por métodos de autenticación
    .dockerignore para archivo dockerfile (https://sites.google.com/coppel.com/developers/estandares/contenedores?authuser=0#h.teppf7oot79k)

### Removed
    Funcionalidades para login custom para fin de apego a norma de Santiago Ontañon para anulación de diversidad de logins
    Variable deviceId (SSO) en archivos de environment

## Comadreja [**1.0.2**] <sub>2022-12-07</sub>

### Fixed
    Dependencia minimist declarada en fake-api/package.json
    Colocación de data titles para routing lazy loading de módulos

### Changed
    Environment cambio de SSO variable appName por channel obtenido del DNS para recaptcha

## Comadreja [**1.0.1**] <sub>2022-11-29</sub>

### Changed
    Cambios de estilos en verificación de componente huella (https://sites.google.com/coppel.com/developers/frameworks/webclient/componentes/componente-huella?authuser=0)

## Comadreja [**1.0.0**] <sub>2022-11-29</sub>

### Changed
    Cambio de versión de beta a release candidate
    Cambio de plataforma de testing Jasmine por Jest

## Comadreja [**0.0.0**] <sub>2022-11-28</sub>

### Added
    Se añade el archivo README.md en donde se describe el contenido del proyecto
    Se añade el arcvhido CHANGELOG.md con un registro de cambios a lo largo del tiempo