import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  inject,
} from '@angular/core';

import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { MatProgressBarModule } from '@angular/material/progress-bar';


import { EstatusComponent } from '../estatus/estatus.component';
import { TablaCabeceros } from 'src/app/interfaces/tabla.interface';
import { PaginadorComponent } from '../paginador/paginador.component';
import { LoaderService } from '../../loader/services/loader.service';


@Component({
  selector: 'app-tabla',
  templateUrl: './tabla.component.html',
  styleUrls: ['./tabla.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    EstatusComponent,
    PaginadorComponent,
    FontAwesomeModule,
    FormsModule,
    MatProgressBarModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TablaComponent {
  @Input() registros: any[] = [];
  @Input() total: number = 0;
  @Input() cabeceros: TablaCabeceros<any>[] = [];
  @Input() registrosPorPagina: number = 10;
  @Input() altura: string = '100%';
  @Input() mensajeVacio = 'Sin registros';
  @Input() mostrarLimites: boolean = false;
  @Input() checkTodos: boolean = false;
  @Input() mostrarMensajeVacio: boolean = true;

  @Input() click: boolean = false;
  @Input() editar: boolean = false;
  @Input() detalle: boolean = false;
  @Input() estatus: boolean = false;
  @Input() descarga: boolean = false;
  @Input() paginado: boolean = false;
  @Input() acciones: boolean = false;
  @Input() scroll: boolean = false;
  @Input() seleccionar: boolean = false;
  @Input() check: boolean = false;
  @Input() radio: boolean = false;
  @Input() eliminar: boolean = false;

  @Input() seleccionado: (registro: any) => boolean;

  @Output() onEditar = new EventEmitter<any>();
  @Output() onDetalle = new EventEmitter<any>();
  @Output() onDescarga = new EventEmitter<any>();
  @Output() onRegistrar = new EventEmitter<any>();
  @Output() onPaginaChange = new EventEmitter<PageChangedEvent>();
  @Output() onClick = new EventEmitter<any>();

  public loaderService = inject(LoaderService);

  @Output() onSeleccionar = new EventEmitter<any>();
  @Output() onSeleccionarTodos = new EventEmitter<any>();
  @Output() onCantidadChange = new EventEmitter<number>();
  @Output() onEliminar = new EventEmitter<any>();
  @Output() registrosPorPaginaChange = new EventEmitter<number>();
  @Output() checkTodosChange = new EventEmitter<boolean>();

  public faTrash = faTrash;
  public esIndeterminado: boolean = false;
  get cantidadColumnas() {
    const columnasExtra = [
      this.acciones,
      this.detalle,
      this.estatus,
      this.acciones,
      this.descarga,
      this.editar,
    ];
    return (
      this.cabeceros.length +
      columnasExtra.map((c) => Number(c)).reduce((a, b) => a + b)
    );
  }

  /**
   * Evento que se emite al cambiar la cantidad de paginas del paginado
   */
  cambiarCantidad(evento): void {
    this.registrosPorPaginaChange.emit(this.registrosPorPagina);
    this.onCantidadChange.emit(evento)
  }

  /**
   * Evento que se emite al seleccionar un registro de la tabla
   */
  checkboxChange(e: Event, registro: any): void {
    if (this.checkTodos) {
      this.esIndeterminado = true;
    }
    this.onSeleccionar.emit({ event: e, registro });
  }

  /**
   * función que se ejecuta al seleccionar el check Todos
   * @param e Evento
   */
  seleccionarTodos(e) {
    this.onSeleccionarTodos.emit(e);
    this.checkTodosChange.emit(e.target.checked);
  }
}
