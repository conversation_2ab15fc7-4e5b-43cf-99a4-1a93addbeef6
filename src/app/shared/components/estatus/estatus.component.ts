import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'app-estatus',
  template: `
    <div>
      <span [ngClass]="{ 'enabled': estatus }">
        {{ estatus ? 'Habilitado' : 'Inhabilitado' }}
      </span>
    </div>
  `,
  standalone: true,
  imports: [CommonModule],
  styleUrls: ['./estatus.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EstatusComponent {
  @Input() estatus: boolean  = true;
}
