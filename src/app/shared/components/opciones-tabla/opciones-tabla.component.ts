import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-opciones-tabla',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="d-flex gap-2">
      <button (click)="editar.emit()" type="button" class="btn btn-icon">
        <img class="cursor-pointer" src="assets/svg/edit.svg" />
      </button>
      <button (click)="eliminar.emit()" type="button" class="btn btn-icon">
        <img class="cursor-pointer" src="assets/svg/delete.svg" />
      </button>
    </div>
  `,
})
export class OpcionesTablaComponent {
  @Output() editar = new EventEmitter<number>();
  @Output() eliminar = new EventEmitter<number>();
}
