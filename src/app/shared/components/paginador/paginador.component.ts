import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PageChangedEvent, PaginationModule } from 'ngx-bootstrap/pagination';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faAnglesLeft, faAnglesRight, faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';

import { CantidadRegistrosService } from 'src/app/services/cantidad-registros.service';

@Component({
  selector: 'app-paginador',
  standalone: true,
  templateUrl: 'paginador.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, PaginationModule, FontAwesomeModule, NgbDropdownModule, FormsModule],
  styleUrls: ['./paginador.component.scss'],
})
export class PaginadorComponent {
  @Input() total: number = 0;
  @Input() registrosPorPagina: number = 0;
  @Input() mostrarLimites: boolean = false;

  @Output() onPaginaChange = new EventEmitter<PageChangedEvent>();
  @Output() onCantidadChange = new EventEmitter<number>();
  @Output() registrosPorPaginaChange = new EventEmitter<number>();

  public chevronLeft = faChevronLeft;
  public chevronRight = faChevronRight;
  public doubleLeft = faAnglesLeft;
  public doubleRight = faAnglesRight;

  constructor(
    public cantidadRegistrosService: CantidadRegistrosService,
  ) { }

  /**
   * Reacciona al cambio de la cantidad de registros por página
   */
  cambiarCantidad(): void {
    this.registrosPorPaginaChange.emit(this.registrosPorPagina);
    this.onCantidadChange.emit(this.registrosPorPagina);
  }
}
