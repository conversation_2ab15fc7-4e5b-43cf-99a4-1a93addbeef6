<div class="row justify-content-between">
  <div class="col-auto">
    <pagination [totalItems]="total" [itemsPerPage]="registrosPorPagina" [maxSize]="5" [boundaryLinks]="mostrarLimites"
      [customFirstTemplate]="firstTemplate" [customLastTemplate]="lastTemplate" [customNextTemplate]="nextTemplate"
      [customPreviousTemplate]="prevTemplate" (pageChanged)="onPaginaChange.emit($event)"></pagination>
    <ng-template #prevTemplate let-disabled="disabled" let-currentPage="currentPage">
      <fa-icon [icon]="chevronLeft"></fa-icon>
    </ng-template>
    <ng-template #nextTemplate let-disabled="disabled" let-currentPage="currentPage">
      <fa-icon [icon]="chevronRight"></fa-icon>
    </ng-template>
    <ng-template #firstTemplate let-disabled="disabled" let-currentPage="currentPage">
      <fa-icon [icon]="doubleLeft"></fa-icon>
    </ng-template>
    <ng-template #lastTemplate let-disabled="disabled" let-currentPage="currentPage">
      <fa-icon [icon]="doubleRight"></fa-icon>
    </ng-template>
  </div>
  <div class="col-auto d-flex align-items-center gap-2 justify-self-end">
    <span>Mostrando</span>
    <select class="form-select mb-1 py-2" [(ngModel)]="registrosPorPagina" (change)="cambiarCantidad()">
      <option *ngFor="let c of cantidadRegistrosService.comboCantidadRegistros" [value]="c.valor">
        {{c.descripcion}}</option>
    </select>
    <span>elementos</span>
  </div>
</div>