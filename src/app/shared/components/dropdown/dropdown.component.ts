import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';

import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { BehaviorSubject, map, tap } from 'rxjs';

@Component({
  selector: 'app-dropdown',
  template: `
    <div class="btn-group d-flex align-items-end" dropdown>
      <label for="dropdown" class="text-secondary">{{ etiqueta }}:</label>
      <a
        class="nav-link dropdown-toggle text-secondary font-weight-bold"
        dropdownToggle
        role="button"
        aria-expanded="false"
      >
        {{ elementoSeleccionado$ | async }}
      </a>
      <ul
        class="dropdown-menu"
        *dropdownMenu
        aria-labelledby="navbarDarkDropdownMenuLink"
        role="menu"
      >
        <li *ngFor="let elemento of seleccionables$ | async" role="menuitem">
          <p
            class="dropdown-item cursor-pointer"
            (click)="seleccion$.next(elemento)"
          >
            {{ elemento[enlaceEtiqueta] }}
          </p>
        </li>
      </ul>
    </div>
  `,
  styles: [
    `
      .dropdown-menu {
        max-height: 340px;
        overflow-y: scroll;
      }
    `,
  ],
  standalone: true,
  imports: [CommonModule, BsDropdownModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DropDownComponent implements OnChanges {
  @Input() etiqueta: string = '';
  @Input() enlaceEtiqueta: string = 'nombre';
  @Input() enlaceValor: string = 'id';
  @Input() elementoSeleccionado: any = null;
  @Input() elementos: any[] = [];
  @Input() default: boolean = true;
  @Input() etiquetaDefault: string = 'Todos';
  @Input() placeholder: string = 'Todos';

  @Output() elementoSeleccionadoChange = new EventEmitter<any>();

  public seleccion$ = new BehaviorSubject<any>(null);

  /**
   * Sincroniza al item seleccionado
   */
  ngOnChanges(changes: SimpleChanges): void {
    const elementoSeleccionado = changes['elementoSeleccionado']?.currentValue;

    if (!elementoSeleccionado) {
      this.seleccion$.next(null);
      return;
    }

    const seleccion = this.elementos.find(
      ({ [this.enlaceValor]: id }) => id === elementoSeleccionado[this.enlaceValor]
    );

    this.seleccion$.next(seleccion);
  }

  public elementoSeleccionado$ = this.seleccion$.pipe(
    tap((item) =>
      this.elementoSeleccionadoChange.emit(item?.valorDefault ? null : item)
    ),
    map((item) => {
      if (!item || item.valorDefault) return this.placeholder;
      return item[this.enlaceEtiqueta];
    })
  );

  public seleccionables$ = this.seleccion$.pipe(
    map((item) => {
      if (!item) return this.elementos;

      const { [this.enlaceValor]: seleccion, valorDefault } = item;

      if (valorDefault) return this.elementos;

      return [
        this.default && {
          [this.enlaceEtiqueta]: this.etiquetaDefault,
          valorDefault: true,
        },
        ...this.elementos.filter(
          ({ [this.enlaceValor]: valor }) => valor !== seleccion
        ),
      ].flatMap((item) => (item ? item : []));
    })
  );
}
