import { environment } from 'src/environments/environment';

const [baseUrl] = environment.interceptorApproach;

export const CATALOGOS = '/catalogos';
export const EMPLEADOS = '/empleados';
export const GENERALES = '/generales';
export const LISTA = '/lista';
export const ME = '/me';
export const MODULOS = '/modulos';
export const PERFILES = '/perfiles';
export const SESIONES = '/sesiones';
export const USUARIO = '/usuario';
export const USUARIOS = '/usuarios';

export const endpoint = (base: string, ...uris: (string | number)[]) =>
  `${baseUrl}${base}${uris
    .map((uri) => (String(uri).startsWith('/') ? uri : `/${uri}`))
    .join('')}`;
