/**
 * Función que convierte un número en formato de moneda
 * @param importe Cantidad que se convertirá en formato de moneda.
 * @returns La cantidad convertida en formato de moneda.
 */
const formatearMoneda = (importe: string) => {
    const moneda = parseInt(importe, 10);
    return moneda.toLocaleString('es-MX', { style: 'currency', currency: 'MXN', maximumFractionDigits: 0 })
};

export default formatearMoneda;
