import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

import { MenuService } from '../services/menu.service';
import { ToasterService } from '../shared/toaster/services/toaster.service';
import { SIN_PERMISO } from '../constants/mensajes';

export const validarPermisoGuard: CanActivateFn = (route) => {
  const router = inject(Router);
  const toasterService = inject(ToasterService);
  const menuService = inject(MenuService);

  // const tienePermiso = menuService.tienePermiso(route);
  const tienePermiso = true;

  if (tienePermiso) {
    return true;
  }
  
  router.navigateByUrl('/');
  toasterService.showWarn(SIN_PERMISO);
  return false;
};


