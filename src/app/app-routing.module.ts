import { NgModule } from '@angular/core';
import { RouterModule, Routes, Router } from '@angular/router';

import { environment } from './../environments/environment';

const isIframe = window !== window.parent && !window.opener;

export const APP_ROUTES: Routes = [
  {
    path: '',
    loadChildren: () => import('./features/features.module').then(m => m.FeaturesModule),
  },
  {
    path: '**',
    redirectTo: '',
    pathMatch: 'full'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(APP_ROUTES, { initialNavigation: !isIframe ? 'enabledNonBlocking' : 'disabled', useHash: true })],
  exports: [RouterModule],
})
export class AppRoutingModule {
  constructor(private router: Router) {
    if (environment.production) {
      this.router.errorHandler = (_error: any) => {
        this.router.navigate(['/']); // or redirect to default route
      };
    }
  }
}
