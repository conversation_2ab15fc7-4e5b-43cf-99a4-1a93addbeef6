import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgModule, LOCALE_ID, APP_INITIALIZER } from '@angular/core';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { registerLocaleData } from '@angular/common';

import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { CollapseModule } from 'ngx-bootstrap/collapse';
import { defineLocale } from 'ngx-bootstrap/chronos';
import { esLocale } from 'ngx-bootstrap/locale';

defineLocale('es', esLocale);
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

import { AppComponent } from './app.component';
import { environment } from '../environments/environment';

import localeEsMx from '@angular/common/locales/es-MX';
registerLocaleData(localeEsMx);

// Import Global Modules
import { AppRoutingModule } from './app-routing.module';
import { ToasterModule } from 'src/app/shared/toaster/toaster.module';
import { LoaderModule } from 'src/app/shared/loader/loader.module';

// Import Global Services
import { AadService } from './services/idps/aad.service';
import { ConfigService } from './services/config.service';
import { SecureStorageService } from './services/secure-storage.service';

// Import Global Helpers
import { AuthGuard } from './guards/auth.guard';
import { JwtInterceptor } from './helpers/jwt.interceptor';

// Import 3rd party components
import { InteractionType, PublicClientApplication } from '@azure/msal-browser';
import { ToastrModule } from 'ngx-toastr';
import {
  MsalGuard,
  MsalGuardConfiguration,
  MsalInterceptor,
  MsalInterceptorConfiguration,
  MsalModule,
  MsalRedirectComponent
} from '@azure/msal-angular';
import { BsModalService } from 'ngx-bootstrap/modal';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { DialogoConfirmacionComponent } from './shared/components/dialogo-confirmacion/dialogo-confirmacion.component';

// MSAL Config
const isIE = window.navigator.userAgent.indexOf('MSIE ') > -1 || window.navigator.userAgent.indexOf('Trident/') > -1;
const { tenantId, clientId, authorityUrl, redirectUri, graphUrl, scopes } = environment.authIdp.AAD;
const msalAuthConfiguration = {
  auth: {
    clientId,
    authority: `${authorityUrl}/${tenantId}`,
    redirectUri: redirectUri,
  },  
  // cache: { cacheLocation: 'sessionStorage', storeAuthStateInCookie: isIE }
};
const msalGuardConfiguration: MsalGuardConfiguration = {
  interactionType: InteractionType.Redirect, // MSAL Guard Configuration
  authRequest: { scopes }
}
const msalInterceptorConfiguration: MsalInterceptorConfiguration = {
  interactionType: InteractionType.Redirect, // MSAL Interceptor Configuration
  protectedResourceMap: new Map([[`${graphUrl}/v1.0/me`, ['User.Read']]]), 
}

// App Initializer Config
export function ConfigLoader(configService: ConfigService) {
  return () => configService.getJSON();
}
export function StorageLoader(storageService: SecureStorageService) {
  return () => {
    storageService.load()
    return storageService.loaded
  };
}

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent, MsalRedirectComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    ToasterModule,
    LoaderModule,
    NgbDatepickerModule,
    BsDropdownModule.forRoot(),
    MsalModule.forRoot(new PublicClientApplication(msalAuthConfiguration), msalGuardConfiguration, msalInterceptorConfiguration),
    CollapseModule.forRoot(),
    BsDatepickerModule.forRoot(),
    ToastrModule.forRoot(),
    DialogoConfirmacionComponent
  ],
  providers: [
    ConfigService,
    SecureStorageService,
    AadService,
    { provide: LOCALE_ID, useValue: 'es-MX' },
    { provide: APP_INITIALIZER, useFactory: ConfigLoader, deps: [ConfigService], multi: true },
    { provide: APP_INITIALIZER, useFactory: StorageLoader, deps: [SecureStorageService], multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: MsalInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    AuthGuard,
    MsalGuard,
    BsModalService,
    provideHttpClient(withInterceptorsFromDi()),
  ]
})
export class AppModule { }
