import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';

import { BehaviorSubject, Observable, catchError, of, switchMap, tap } from 'rxjs';

import { UsuarioSesion } from '../interfaces/usuario-sesion.interface';
import { ME, SESIONES, endpoint } from '../constants/uris';
import { RespuestaMe } from '../interfaces/session-service.interface';

@Injectable({
  providedIn: 'root',
})
export class SessionService {
  private router = inject(Router);
  private http = inject(HttpClient);

  private currentUserSubject: BehaviorSubject<UsuarioSesion> =
    new BehaviorSubject(null);
  
  public readonly currentUser: Observable<UsuarioSesion> =
    this.currentUserSubject.asObservable();

  /**
   * Método que establece el usuario actual de la
   * sesión
   */
  public setCurrentUser(currentUser: UsuarioSesion): void {
    this.currentUserSubject.next(currentUser);
  }

  /**
   * Método que obtiene al usuario actual en sesión
   */
  public getCurrentUser() {
    return this.currentUserSubject.getValue();
  }

  /**
   * Método que cierra sesión 
   */
  public logout() {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
    return false;
  }

  /**
   * Método que envia un petición HTTP para autenticar al usuario
   * con el sistema de monitores
   */
  public autenticarUsuario() {
    return this.http.get<RespuestaMe>(endpoint(SESIONES, ME)).pipe(
      tap((res) => {
        const usuario = res satisfies UsuarioSesion;
        this.setCurrentUser(usuario);
      }),
      switchMap(() => of(true)),
      catchError((error) => of(false))
    );
  }

}
