.root {
  background: linear-gradient(270deg, #FFE144 0.42%, #FFF 98.92%);
  width: 100vw;
  min-height: 100svh;
}

.login-form {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.82);
  background-color: white;
  gap: 40px;
  max-width: 360px;
  transition: all 0.3 ease-in-out;
}

.icono-coppel {
  width: 30%;
  border-radius: 8px;
}

.gap-4 {
  gap: 32px
}

.controles {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-control {
  border-radius: 4px;
  background: #F5F8FA;
  padding: 18px;
}

.btn-primary {
  border-radius: 4px;
  border: 1px solid #E1E7ED;
  background: #31346B;
  padding: 12px;
}

p {
  color: gray
}