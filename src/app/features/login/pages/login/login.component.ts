import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { ToasterService } from '@app-toaster';
import { CaptchaApiService } from '@app-recaptcha';
import { AuthenticationService } from 'src/app/services/authentication.service';
import { environment } from 'src/environments/environment';
import { TiposLoginEnum } from 'src/app/enums/tipos-login.enum';

@Component({
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit, OnDestroy {
  public tipoAutorizacion = environment.authMechanisms;
  public tipoLogin!: string;
  public loginUnico!: string;

  public tiposLogin = TiposLoginEnum

  constructor(
    private route: ActivatedRoute,
    private auth: AuthenticationService,
    private router: Router,
    private toaster: ToasterService,
    private recaptcha: CaptchaApiService
  ) {
    let loginsConFormulario = []
    let loginsConBoton = []

    for (const [idp, habilitado] of Object.entries(this.tipoAutorizacion)) {
      if (habilitado && idp !== 'authCode') {
        loginsConBoton.push(idp)
        if (idp !== ' ') {
          loginsConFormulario.push(idp)
        }
      }
    }

  }

  public ngOnInit() {
    this.verificarSesion()
    this.recaptcha.showBadge();
  }
  
  public ngOnDestroy(): void {
    this.recaptcha.hideBadge();
  }

  /**
   * Método que limpia el tipo de login seleccionado
   */
  public regresar() {
    this.tipoLogin = '';
  }

  /**
   * Método que verifica si existe una sesión guardada
   */
  public verificarSesion() {
    const existeIdp = typeof sessionStorage.getItem('idp') == 'string';
    this.route.queryParams.subscribe(({token}) => {
      if (token) {
        this.auth.login('authCode', token);
      } else if (existeIdp) {
        this.router.navigate(['']);
      }
    });
  }

  /**
   * Método que consulta los servicios para el inicio de sesión
   */
  public async login(mecanismoAutorizacion: string, credenciales: any = {}) {
    if (mecanismoAutorizacion != 'colaboradorDigital') {
      return this.auth.login(mecanismoAutorizacion, credenciales)
    }
    try {
      const tokenCaptcha: string = await this.recaptcha.getToken('createAccount');
      this.auth.login(mecanismoAutorizacion, credenciales, tokenCaptcha)
    } catch (error) {
      this.toaster.showError('Ocurrió un error inesperado. Intente más tarde.')
    }
  }
}

