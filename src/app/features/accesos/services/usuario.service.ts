import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';

import { Paginado } from '@app-interfaces/paginado.interface';
import { Empleado } from '@app-interfaces/empleado.interface';
import { Usuario } from '@app-interfaces/usuario-monitor.interface';
import { CATALOGOS, EMPLEADOS, LISTA, USUARIOS, endpoint } from '@app-constants/uris';

@Injectable({
  providedIn: 'root',
})
export class UsuarioService {
  private http = inject(HttpClient);

  /**
   * Petición para obtener el listado del catalogo usuarios
   */
  public obtenerCatalogo(
    pagina: number,
    registrosPorPagina: number,
    busqueda?: string
  ) {
    return this.http.get<Paginado<Empleado>>(endpoint(CATALOGOS, EMPLEADOS), {
      params: {
        pagina,
        registrosPorPagina,
        busqueda,
      },
    });
  }

  /**
   * Petición para obtener el listado de usuarios
   */
  public obtenerListado(
    pagina: number,
    registrosPorPagina: number,
    busqueda?: string
  ) {
    return this.http.get<Paginado<Usuario>>(endpoint(USUARIOS, LISTA), {
      params: {
        pagina,
        registrosPorPagina,
        busqueda,
      },
    });
  }

  /**
   * Petición para guardar un usuario nuevo del monitor
   */
  public guardarUsuario(usuario: Usuario) {
    return this.http.post(endpoint(USUARIOS), usuario);
  }

  /**
   * Petición para editar un usuario del monitor
   */
  public editarUsuario(idu_usuario: number, usuario: Usuario) {
    return this.http.put(endpoint(USUARIOS, idu_usuario), usuario);
  }

  /**
   * Petición para eliminar un usuario del monitor
   */
  public eliminarUsuario(idu_usuario: number) {
    return this.http.delete(endpoint(USUARIOS, idu_usuario));
  }
}
