import {
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
  inject,
} from '@angular/core';

import {
  BehaviorSubject,
  Observable,
  of,
} from 'rxjs';

import { ModalService } from 'src/app/services/modal.service';
import { Empleado } from 'src/app/interfaces/empleado.interface';
import { Usuario } from 'src/app/interfaces/usuario-monitor.interface';

@Component({
  selector: 'app-ayuda-usuario',
  templateUrl: './ayuda-usuario.component.html',
  styleUrls: ['./ayuda-usuario.component.scss'],
})
export class AyudaUsuarioComponent {
  @ViewChild('modal') modal: TemplateRef<{}>;

  @Input() busqueda: boolean = true;

  @Input() set usuarioMonitor(usuario: Usuario | null) {
    if (!usuario) return;

    this.empleadoSeleccionado$.next({
      nombre: usuario.nom_usuario,
      puesto: usuario.nom_puesto,
      telefono: usuario.num_telefono,
    });
  }

  @Output() seleccion = new EventEmitter<Empleado>();

  private modalService = inject(ModalService);

  public pagina$ = new BehaviorSubject<number>(1);
  public busqueda$ = new BehaviorSubject<string>('');
  public empleadoSeleccionado$ = new BehaviorSubject<Empleado | null>(null);

  /**
   * Método para seleccionar un empleado
   */
  public seleccionarEmpleado(empleado: Empleado) {
    this.empleadoSeleccionado$.next(empleado);
    this.seleccion.emit(empleado);
    this.modalService.cerrarModal();
  }

  /**
   * Método para abrir el modal de búsqueda
   */
  public abrirModal() {
    this.modalService.abrirModal(this.modal, {
      class: 'modal-lg',
    });
    setTimeout(() => {
      this.pagina$.next(1);
    }, 100);
  }

  /**
   * Método para cerrar el modal de búsqueda
   */
  public cerrar() {
    this.modalService.cerrarModal();
  }

  /**
   * Método para obtener el empleado seleccionado
   */
  public obtenerEmpleadoSeleccionado(): Observable<Empleado | null> {
    if (this.empleadoSeleccionado$ instanceof BehaviorSubject) {
      return this.empleadoSeleccionado$;
    }
    return of(null);
  }

  /**
   * Método para limpiar la selección
   */
  public limpiarAyuda() {
    this.empleadoSeleccionado$.next(null);
    this.seleccion.emit(null);
    this.busqueda$.next('');
    this.pagina$.next(1);
  }
}
