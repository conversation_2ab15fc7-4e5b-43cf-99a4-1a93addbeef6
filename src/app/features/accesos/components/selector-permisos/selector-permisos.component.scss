$selected-bg-color: #EAF0F6;
$control-color: #4B5D6E;
$border: 1px solid #EBEBEB;

.root {
  max-height: 420px;
  overflow: scroll;
}

.controls {
  display: flex;
  align-content: baseline;
  gap: 4px;
  color: $control-color;
  cursor: pointer;
  border: none !important;
  padding: 0.8rem !important;

  &.selected {
    background-color: $selected-bg-color;
  }
}

.cabecero {
  flex: 1;
  display: flex;
  justify-content: center;
}

.modulos {
  td {
    vertical-align: top;
    padding: 0;
    width: 25%;
    max-width: 25%;
    border-bottom: 0;
    height: 420px;
  }

  & td:not(:first-child) {
    border-left: $border;
    border-right: $border;
  }

  & td:last-child {
    border: 0 !important;
  }
}

.modulo-label {
  padding: 0;
  font-size: 14px;
  white-space: nowrap;
  padding-left: 0.4rem;
  text-overflow: ellipsis;
  max-width: 80%;
  overflow: hidden;
  cursor: pointer;
}

label {
  margin: 0;
}