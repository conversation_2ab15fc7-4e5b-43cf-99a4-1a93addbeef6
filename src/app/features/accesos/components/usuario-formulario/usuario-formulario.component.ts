import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  inject,
} from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';

import { BehaviorSubject, Observable, map, of, tap } from 'rxjs';

import { DialogoConfirmacionService } from '@app-services/dialogo-confirmacion.service';
import { Rol } from '@app-interfaces/rol.interface';
import { Modulo } from '@app-interfaces/modulo.interface';
import { Empleado } from '@app-interfaces/empleado.interface';
import { Usuario } from '@app-interfaces/usuario-monitor.interface';
import { RolService } from '../../services/rol.service';

import { AyudaUsuarioComponent } from '../ayuda-usuario/ayuda-usuario.component';

@Component({
  selector: 'app-usuario-formulario',
  templateUrl: './usuario-formulario.component.html',
  styleUrls: ['usuario-formulario.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UsuarioFormularioComponent implements OnInit {
  @ViewChild(AyudaUsuarioComponent) ayudaUsuario: AyudaUsuarioComponent;

  @Input() set usuarioMonitor(usuario: Usuario | null) {
    this.usuarioMonitor$.next(usuario);

    const { perfil } = usuario || {}
    const existePerfil = this.roles.find(
      ({ idu_si_rol }) => idu_si_rol === perfil?.idu_si_rol
    );

    this.formulario.patchValue({
      ...usuario,
      idu_si_rol: existePerfil ? usuario?.perfil.idu_si_rol : null,
    });
  }

  @Output() cerrar = new EventEmitter<void>();
  @Output() guardar = new EventEmitter<Usuario>();

  private fb = inject(FormBuilder);
  private rolService = inject(RolService);
  private dialogoConfirmacionService = inject(DialogoConfirmacionService);

  public usuarioMonitor$ = new BehaviorSubject<Usuario | null>(null);
  public mostrarBotonBusqueda$ = this.usuarioMonitor$.pipe(
    map((usuario) => !usuario)
  );

  public formulario = this.fb.group({
    idu_si_usuario: [null],
    idu_si_rol: new FormControl<number | null>(null, [Validators.required]),
    opc_activo: new FormControl<boolean>(true, [Validators.required]),
  });

  public roles: Rol[] = [];
  public roles$ = this.rolService.obtenerTodos().pipe(
    tap((roles) => this.roles = roles)
  );

  public rolSeleccionado$: Observable<Rol | null>;
  public modulosRolSeleccionado$: Observable<Modulo[]> = of(null);

  public empleadoSeleccionado$ = new BehaviorSubject<Empleado>(null);
  public errorUsuarioRequerido$ = new BehaviorSubject<boolean>(false);

  ngOnInit(): void {
    this.rolSeleccionado$ = this.formulario
      .get('idu_si_rol')
      .valueChanges.pipe(
        map((idu_si_rol) =>
          this.roles.find(
            ({ idu_si_rol: idu }) => idu === Number(idu_si_rol)
          )
        )
      );

    this.modulosRolSeleccionado$ = this.rolSeleccionado$.pipe(
      map((perfil) =>
        perfil
          ? perfil.modulos
          : null
      )
    );
  }

  /**
   * Método que emite un evento al dar click en guardar al formulario
   */
  public guardarFormulario() {
    let formularioInvalido = false;

    if (this.formulario.invalid) {
      this.formulario.markAllAsTouched();
      formularioInvalido = true;
    }

    let usuarioMonitor: Usuario;
    if (this.usuarioMonitor$.getValue() && !formularioInvalido) {
      usuarioMonitor = {
        ...this.formulario.value,
      };
      this.guardar.emit(usuarioMonitor);
      this.limpiarFormulario();
      return;
    }

    const empleado = this.empleadoSeleccionado$.getValue();
    if (!empleado && !this.usuarioMonitor$.getValue()) {
      formularioInvalido = true;
      this.errorUsuarioRequerido$.next(true);
    }

    if (formularioInvalido) return;

    usuarioMonitor = {
      ...this.formulario.value,
      num_empleado: empleado.idu_empleado,
      nom_usuario: empleado.nombre,
      num_telefono: empleado.telefono,
      nom_puesto: empleado.puesto,
    };

    this.guardar.emit(usuarioMonitor);
    this.limpiarFormulario();
  }

  /**
   * Método para cerrar el formulario
   */
  public async cerrarFormulario() {
    if (this.formulario.touched) {
      const confirmado = await this.dialogoConfirmacionService.mostrarDialogo();
      if (!confirmado) return;
    }
    this.cerrar.emit();
    this.limpiarFormulario();
  }

  /**
   * Método que limpia y reinicia el formulario
   */
  private limpiarFormulario() {
    this.formulario.reset();
    this.formulario.patchValue({
      opc_activo: true,
      idu_si_rol: null,
    });
    this.usuarioMonitor$.next(null);
    this.errorUsuarioRequerido$.next(false);
    this.ayudaUsuario.limpiarAyuda();
  }

  /**
   * Método para cancelar el formulario
   */
  public cancelar() {
    this.limpiarFormulario();
  }
}
