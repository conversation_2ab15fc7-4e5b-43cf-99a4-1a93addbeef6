import { Component, EventEmitter, Output, inject } from '@angular/core';

import { BehaviorSubject, combineLatest, debounceTime, shareReplay, switchMap } from 'rxjs';

import { UsuarioService } from '../../services/usuario.service';
import { REGISTROS_POR_PAGINA } from '@app-constants/general';
import { TablaCabeceros } from '@app-interfaces/tabla.interface';
import { Empleado } from '@app-interfaces/empleado.interface';
import { Paginado } from '@app-interfaces/paginado.interface';

@Component({
  selector: 'app-modal-ayuda-usuario',
  templateUrl: './modal-ayuda-usuario.component.html',
})
export class ModalAyudaUsuarioComponent {
  @Output() seleccion = new EventEmitter<Empleado>()

  private usuarioService = inject(UsuarioService);

  public busqueda$ = new BehaviorSubject<string>('');
  public pagina$ = new BehaviorSubject<number>(1);
  public registrosPorPagina$ = new BehaviorSubject<number>(REGISTROS_POR_PAGINA);

  public listado$ = combineLatest([
    this.pagina$,
    this.registrosPorPagina$,
    this.busqueda$,
  ]).pipe(
    debounceTime(300),
    switchMap(([pagina, registrosPorPagina, busqueda]) => this.usuarioService.obtenerCatalogo(
      pagina, registrosPorPagina, busqueda
    )),
    shareReplay(1),
  );

  public cabeceros: TablaCabeceros<Empleado>[] = [
    {
      key: 'nombre',
      label: 'Usuario',
    },
    {
      key: 'puesto',
      label: 'Puesto',
    },
    {
      key: 'telefono',
      label: 'Teléfono',
    },
  ];

}
