<app-modal-container *ngIf="listado$ | async as listado" titulo="Búsqueda de usuario">
  <div class="d-flex flex-column" style="gap: 20px">
    <div class="d-flex">
      <input 
        #txtBusqueda
        (change)="busqueda$.next(txtBusqueda.value)"
        placeholder="Búsqueda por nombre" 
        type="text" 
        class="form-control"
      >
    </div>
    <app-tabla 
      [cabeceros]="cabeceros" 
      [registros]="listado.registros" 
      [total]="listado.total" 
      [paginado]="true"
      [click]="true"
      (onClick)="seleccion.emit($event)"
      (onPaginaChange)="pagina$.next($event.page)"
      (onCantidadChange)="registrosPorPagina$.next($event)"
    />
  </div>
</app-modal-container>
