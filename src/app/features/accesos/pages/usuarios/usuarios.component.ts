import { ChangeDetectionStrategy, Component, inject } from '@angular/core';

import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  switchMap,
  shareReplay,
  Subject,
} from 'rxjs';

import { TablaCabeceros } from '@app-interfaces/tabla.interface';
import { Usuario } from '@app-interfaces/usuario-monitor.interface';
import { DrawerService } from '@app-services/drawer.service';
import { ToasterService } from '@app-toaster';
import {
  USUARIO_EDITADO,
  USUARIO_ELIMINADO,
  USUARIO_REGISTRADO,
} from '@app-constants/mensajes';
import { REGISTROS_POR_PAGINA } from '@app-constants/general';
import { UsuarioService } from '../../services/usuario.service';

@Component({
  selector: 'app-usuarios',
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <app-listado
      titulo="Accesos a usuarios"
      [cabeceros]="cabeceros"
      [paginado]="paginado$ | async"
      (agregar)="agregar()"
      (editar)="editarUsuario($event)"
      (eliminar)="eliminarUsuario($event)"
      (paginaCambio)="pagina$.next($event)"
      (busqueda)="busqueda$.next($event)"
      (registrosPorPaginaCambio)="registrosPorPagina$.next($event)"
    />
    <app-drawer>
      <app-usuario-formulario
        [usuarioMonitor]="usuarioEdicion$ | async"
        (cerrar)="cerrarFormulario()"
        (guardar)="guardarUsuario($event)"
      />
    </app-drawer>
  `,
})
export class UsuariosComponent {
  private drawerService = inject(DrawerService);
  private usuarioService = inject(UsuarioService);
  private toasterService = inject(ToasterService);

  public pagina$ = new BehaviorSubject<number>(1);
  public busqueda$ = new BehaviorSubject<string>('');
  public recargar$ = new BehaviorSubject<number>(1);
  public usuarioEdicion$ = new Subject<Usuario | null>();
  public registrosPorPagina$ = new BehaviorSubject<number>(
    REGISTROS_POR_PAGINA
  );

  public paginado$ = combineLatest([
    this.pagina$,
    this.busqueda$,
    this.registrosPorPagina$,
    this.recargar$,
  ]).pipe(
    debounceTime(300),
    switchMap(([pagina, busqueda, registrosPorPagina]) =>
      this.usuarioService.obtenerListado(pagina, registrosPorPagina, busqueda)
    ),
    shareReplay(1)
  );

  public cabeceros: TablaCabeceros<Usuario>[] = [
    {
      label: 'Nombre',
      key: 'nom_usuario',
    },
    {
      label: 'Perfil',
      transform: ({ perfil }) => perfil?.nom_rol,
    },
    {
      label: 'Puesto',
      key: 'nom_puesto',
    },
  ];

  /**
   * Método que levanta el drawer para agregar un usuario
   */
  public agregar() {
    this.drawerService.mostrarDrawer();
  }

  /**
   * Método que cierra el drawer
   */
  public cerrarFormulario() {
    this.usuarioEdicion$.next(null);
    this.drawerService.cerrarDrawer();
  }

  /**
   * Método que realiza una emisión para recargar el listado
   */
  public recargarListado() {
    this.recargar$.next(this.recargar$.getValue() + 1);
  }

  /**
   * Método para guardar o editar un usuario del monitor
   */
  public guardarUsuario(usuario: Usuario) {
    this.drawerService.cerrarDrawer();

    const { idu_si_usuario: idu_usuario } = usuario;
    delete usuario.idu_si_usuario;

    if (!idu_usuario) {
      this.usuarioService.guardarUsuario(usuario).subscribe({
        next: () => {
          this.toasterService.showApproval(USUARIO_REGISTRADO);
          this.recargarListado();
        },
      });
      return;
    }

    this.usuarioService.editarUsuario(idu_usuario, usuario).subscribe({
      next: () => {
        this.toasterService.showApproval(USUARIO_EDITADO);
        this.recargarListado();
      },
    });
  }

  /**
   * Método que levanta el drawer con usuario para editar
   */
  public editarUsuario(usuario: Usuario) {
    this.usuarioEdicion$.next(usuario);
    this.drawerService.mostrarDrawer();
  }

  /**
   * Método para eliminar un usuario del monitor
   */
  public async eliminarUsuario(usuario: Usuario) {
    this.usuarioService.eliminarUsuario(usuario.idu_si_usuario).subscribe({
      next: () => {
        this.toasterService.showApproval(USUARIO_ELIMINADO);
        this.recargarListado();
      },
    });
  }
}
