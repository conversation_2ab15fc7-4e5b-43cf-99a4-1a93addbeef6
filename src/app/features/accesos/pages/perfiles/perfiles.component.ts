import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';

import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  shareReplay,
  switchMap,
} from 'rxjs';

import { TablaCabeceros } from '@app-interfaces/tabla.interface';
import { REGISTROS_POR_PAGINA } from '@app-constants/general';
import { Rol } from '@app-interfaces/rol.interface';
import { DialogoConfirmacionService } from '@app-dialogo-confirmacion';
import { ToasterService } from '@app-toaster';
import { ELIMINAR_PERFIL } from '@app-constants/mensajes';
import { RolService } from '../../services/rol.service';

@Component({
  selector: 'app-perfiles',
  template: `
    <app-listado
      titulo="Perfiles"
      [cabeceros]="cabeceros"
      [paginado]="paginado$ | async"
      (agregar)="agregar()"
      (paginaCambio)="pagina$.next($event)"
      (busqueda)="busqueda$.next($event)"
      (editar)="editar($event)"
      (eliminar)="eliminarPerfil($event)"
      (registrosPorPaginaCambio)="registrosPorPagina$.next($event)"
    />
  `,
})
export class PerfilesComponent {
  private router = inject(Router);
  private dialogoConfirmacionService = inject(DialogoConfirmacionService);
  private toastService = inject(ToasterService);
  private perfilService = inject(RolService);

  public cabeceros: TablaCabeceros<Rol>[] = [
    {
      label: 'Nombre',
      key: 'nom_rol',
    },
    {
      label: 'Descripción',
      key: 'des_rol',
    },
  ];

  public pagina$ = new BehaviorSubject(1);
  public busqueda$ = new BehaviorSubject('');
  public recargar$ = new BehaviorSubject<number>(1);
  public registrosPorPagina$ = new BehaviorSubject<number>(REGISTROS_POR_PAGINA);

  public paginado$ = combineLatest([
    this.pagina$,
    this.busqueda$,
    this.registrosPorPagina$,
    this.recargar$,
  ]).pipe(
    debounceTime(300),
    switchMap(([pagina, busqueda, registrosPorPagina]) =>
      this.perfilService.obtenerListado(pagina, registrosPorPagina, busqueda)
    ),
    shareReplay(1)
  );

  /**
   * Función que redirige al formulario
   */
  public agregar() {
    this.router.navigateByUrl('accesos/perfiles/agregar');
  }

  /**
   * Función que redirige al formulario de edición
   */
  public editar(perfil: Rol) {
    this.router.navigateByUrl(`accesos/perfiles/${perfil.idu_si_rol}`);
  }

  /**
   * Función para eliminar un perfil
   */
  public async eliminarPerfil(perfil: Rol) {
    this.perfilService.eliminarRol(perfil.idu_si_rol).subscribe({
      next: () => {
        this.toastService.showApproval(ELIMINAR_PERFIL);
        this.recargar$.next(this.recargar$.getValue() + 1);
      },
    });
  }
}
