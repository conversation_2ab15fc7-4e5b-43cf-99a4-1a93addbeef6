import { Injectable } from '@angular/core';

import { of } from 'rxjs';

import { TipoServicio } from '../constants/conciliaciones';
import { CONCILIACIONES } from '../constants/conciliaciones';

@Injectable({
  providedIn: 'root'
})
export class ConciliacionesService {

  /**
  * Función que retorna los servicio de un tipo
  * de servicio
  */
  public obtenerServicios(tipoServicio?: TipoServicio) {
    if (tipoServicio)
      return of(
        CONCILIACIONES.filter(({ tipo }) => tipo === tipoServicio)
      );

    return of(CONCILIACIONES);
  }
}
