import { Injectable, TemplateRef } from '@angular/core';

import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modalRef: BsModalRef = null;

  constructor(
    private modalService: BsModalService,
  ) { }

  /**
   * Método que abre un modal
   */
  abrirModal(component: string | TemplateRef<any> | (new (...args: any[]) => unknown), modalOptions?: ModalOptions<unknown>) {
    this.modalRef = this.modalService.show(component, modalOptions);
    return this.modalRef;
  }

  /**
   * Metodo que cierra el modal especificado
   */
  cerrarModal(modal: BsModalRef = null) {
    this.modalService.hide(modal?.id || this.modalRef?.id);
  }
}
