import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import _sodium from 'libsodium-wrappers';

@Injectable({
  providedIn: 'root'
})
export class SecureStorageService {

  public loaded;
  public key!: Uint8Array;
  private nonceBytes!: number;

  constructor() {
    this.loaded = _sodium.ready;
  }

  public load(): void {
    this.loaded.then(() => {
      this.nonceBytes = _sodium.crypto_aead_xchacha20poly1305_ietf_NPUBBYTES;

      // Cargar o generar la clave
      const storedKey = sessionStorage.getItem('secure_key');
      if (storedKey) {
        this.key = _sodium.from_base64(storedKey);
      } else {
        const generatedKey = _sodium.crypto_generichash(32, _sodium.from_string(environment.appId));
        this.key = generatedKey;
        sessionStorage.setItem('secure_key', _sodium.to_base64(this.key));
      }
    });
  }

  private encrypt(message: string): string {
    const nonce = _sodium.randombytes_buf(this.nonceBytes);
    const ciphertext = _sodium.crypto_aead_xchacha20poly1305_ietf_encrypt(
      message, null, nonce, nonce, this.key
    );
    return _sodium.to_hex(nonce) + _sodium.to_hex(ciphertext);
  }

  private decrypt(nonce_and_ciphertext_str: string, key: string): string {
    try {
      const nonceSize = this.nonceBytes * 2;
      const nonce = _sodium.from_hex(nonce_and_ciphertext_str.slice(0, nonceSize));
      const ciphertext = _sodium.from_hex(nonce_and_ciphertext_str.slice(nonceSize));
      const decryptedData = _sodium.crypto_aead_xchacha20poly1305_ietf_decrypt(
        nonce, ciphertext, null, nonce, this.key, 'text'
      );
      return decryptedData;
    } catch (error) {
      sessionStorage.removeItem(key);
      return '';
    }
  }

  public setItem(key: string, value: string): void {
    const encrypted = this.encrypt(value);
    sessionStorage.setItem(key, encrypted);
  }

  public getItem(key: string): string {
    const encrypted = sessionStorage.getItem(key);
    if (!encrypted) return '';
    return this.decrypt(encrypted, key);
  }

  public removeItem(key: string): void {
    sessionStorage.removeItem(key);
  }
}
