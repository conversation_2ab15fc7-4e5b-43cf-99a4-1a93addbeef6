import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class SidebarService {

  private _isSidebarOpen: boolean = true;

  get isSidebarOpen(): boolean {
    return this._isSidebarOpen;
  }

  setSidebarOpen() {
    this._isSidebarOpen = true;
    document.querySelector('app-root')!.classList.remove('sidebar-hidden');
  }

  setSidebarClose() {
    this._isSidebarOpen = false;
    document.querySelector('app-root')!.classList.add('sidebar-hidden');
  }

  toggleSidebar() {
    this._isSidebarOpen = !this._isSidebarOpen;
    document.querySelector('app-root')!.classList.toggle('sidebar-hidden');
  }

}
