<header *ngIf="usuario$ | async as usuario" class="navbar navbar-ligth px-4 py-3" style="background-color: #FFF;">
  <div class="container-fluid">
    <div class="logo-container">
      <img src="assets/svg/coppel-logo.svg" alt="" class="logo">
      <div class="title">
        <h1>SISTEMA DE INGRESOS</h1>
        <h3>GESTOR</h3>
      </div>
    </div>
    <div class="user px-3">
      <a class="text-decoration-none" href (click)="false" [matMenuTriggerFor]="menu">
        {{usuario.nom_usuario}}
        <fa-icon [icon]="iconoCaretDown" class="ms-1 cursor-pointer" />
      </a>
    </div>
  </div>
</header>

<mat-menu #menu>
  <menu mat-menu-item (click)="cerrarSesion()" class="p-1 text-center" style="min-height: 30px;">
    <span style="font-size: 0.9rem;"><PERSON><PERSON><PERSON> se<PERSON></span>
  </menu>
</mat-menu>