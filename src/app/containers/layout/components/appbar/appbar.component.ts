import { ChangeDetectionStrategy, Component, inject } from '@angular/core';

import { faCaretDown } from '@fortawesome/free-solid-svg-icons';

import { SessionService } from 'src/app/state/session.service';

@Component({
  selector: 'appbar',
  templateUrl: './appbar.component.html',
  styleUrls: ['./appbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppbarComponent {
  private sessionService = inject(SessionService);

  public nombreUsuario = 'Administrador';
  public iconoCaretDown = faCaretDown;

  public usuario$ = this.sessionService.currentUser;

  public cerrarSesion() {
    this.sessionService.logout();
  }

}
