import { Component, Input } from '@angular/core';

import { SidebarItem } from '@app-interfaces/sidebar.interface';

@Component({
  selector: 'app-sidebar-link',
  template: `
    <a class="menu-link" [routerLink]="url" routerLinkActive="active">
      {{menuItem.name}}
    </a>
  `,
  styleUrls: ['./sidebar-link.component.scss']
})
export class SidebarLinkComponent {
  @Input() menuItem: SidebarItem;

  public get url(): string {
    return this.menuItem.url;
  }
}
