import { Component, inject } from '@angular/core';
import { Observable, of } from 'rxjs';

import { MenuService } from 'src/app/services/menu.service';

@Component({
  selector: 'app-sidebar',
  styleUrls: ['./sidebar.component.scss'],
  template: `
    <nav class="menu" [ngClass]="{ open: (getMenuOpen() | async) }">
      <div class="sidebar-open-btn">
        <app-sidebar-open-button />
      </div>
      <ul class="menu-items position-relative overflow-hidden">
        <ng-container *ngFor="let menuItem of menu$ | async">
          <app-sidebar-item [menuItem]="menuItem" />
        </ng-container>
      </ul>
    </nav>
  `,
})
export class SidebarComponent {
  private menuService = inject(MenuService);

  public menu$ = this.menuService.currentMenu$;

  getMenuOpen() {
    if (this.menuService.isMenuOpen$ instanceof Observable) {
      return this.menuService.isMenuOpen$;
    }

    return of(false);
  }
}
