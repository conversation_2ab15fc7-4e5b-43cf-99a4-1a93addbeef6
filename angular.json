{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "npm", "analytics": false}, "newProjectRoot": "projects", "projects": {"coppelframework-webclient-angularspa": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": {"base": "dist"}, "index": "src/index.html", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/scss/styles.scss", "node_modules/font-awesome/css/font-awesome.css", "node_modules/ngx-toastr/toastr.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.min.js"], "allowedCommonJsDependencies": ["libsodium-wrappers"], "browser": "src/main.ts", "stylePreprocessorOptions": {"includePaths": ["."]}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "6mb", "maximumError": "12mb"}, {"type": "anyComponentStyle", "maximumWarning": "6mb", "maximumError": "12mb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "coppelframework-webclient-angularspa:build:production"}, "development": {"buildTarget": "coppelframework-webclient-angularspa:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "coppelframework-webclient-angularspa:build"}}}}}}