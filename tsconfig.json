/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": false,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "useDefineForClassFields": false,
    "lib": [
      "ES2022",
      "dom"
    ],
    "paths": {
      "@app-toaster": [
        "src/app/shared/toaster/toaster.module"
      ],
      "@app-loader": [
        "src/app/shared/loader/loader.module"
      ],
      "@app-recaptcha": [
        "src/app/shared/recaptcha-v3/recaptcha-v3.module"
      ],
      "@app-dialogo-confirmacion": [
        "./src/app/services/dialogo-confirmacion.service.ts",
      ],
      "@app-interfaces/*": [
        "./src/app/interfaces/*"
      ],
      "@app-services/*": [
        "./src/app/services/*"
      ],
      "@app-constants/*": [
        "./src/app/constants/*",
      ],
      "@app-components/*": [
        "src/app/shared/components/*",
      ],
      "@app-shared/*": [
        "src/app/shared/*",
      ],
    },
    "esModuleInterop": true
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
